---
title: Phonic AI Chat
emoji: 🎙️
colorFrom: purple
colorTo: red
sdk: gradio
sdk_version: 5.16.0
app_file: app.py
pinned: false
license: mit
short_description: Talk to Phonic AI's speech-to-speech model
tags: [webrtc, websocket, gradio, secret|TWILIO_ACCOUNT_SID, secret|TWILIO_AUTH_TOKEN, secret|PHONIC_API_KEY]
python_version: 3.11
---

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference