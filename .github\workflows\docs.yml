name: docs
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

permissions:
  contents: write
  pull-requests: write
  deployments: write
  pages: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Configure Git Credentials
        run: |
          git config user.name github-actions[bot]
          git config user.email 41898282+github-actions[bot]@users.noreply.github.com
      - uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - run: echo "cache_id=$(date --utc '+%V')" >> $GITHUB_ENV 
      - uses: actions/cache@v4
        with:
          key: mkdocs-material-${{ env.cache_id }}
          path: .cache
          restore-keys: |
            mkdocs-material-
      - run: pip install mkdocs-material
      - name: Build docs
        run: mkdocs build

      - name: Deploy to GH Pages (main)
        if: github.event_name == 'push'
        run: mkdocs gh-deploy --force

      - name: Deploy PR Preview
        if: github.event_name == 'pull_request'
        uses: rossjrw/pr-preview-action@v1
        with:
          source-dir: ./site
          preview-branch: gh-pages
          umbrella-dir: pr-preview
          action: auto