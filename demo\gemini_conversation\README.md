---
title: Gemini Talking to Gemini
emoji: ♊️
colorFrom: purple
colorTo: red
sdk: gradio
sdk_version: 5.17.0
app_file: app.py
pinned: false
license: mit
short_description: Have two Gemini agents talk to each other
tags: [webrtc, websocket, gradio, secret|TWILIO_ACCOUNT_SID, secret|TWILIO_AUTH_TOKEN, secret|GEMINI_API_KEY]
---

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference