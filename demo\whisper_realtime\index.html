<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Whisper Transcription</title>
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #f9a45c 0%, #e66465 100%);
            --background-cream: #faf8f5;
            --text-dark: #2d2d2d;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background-cream);
            color: var(--text-dark);
            min-height: 100vh;
        }

        .hero {
            background: var(--primary-gradient);
            color: white;
            padding: 2.5rem 2rem;
            text-align: center;
        }

        .hero h1 {
            font-size: 2.5rem;
            margin: 0;
            font-weight: 600;
            letter-spacing: -0.5px;
        }

        .hero p {
            font-size: 1rem;
            margin-top: 0.5rem;
            opacity: 0.9;
        }

        .container {
            max-width: 1000px;
            margin: 1.5rem auto;
            padding: 0 2rem;
        }

        .transcript-container {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 1.5rem;
            height: 300px;
            overflow-y: auto;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .controls {
            text-align: center;
            margin: 1.5rem 0;
        }

        button {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 0.95rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
            min-width: 180px;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(230, 100, 101, 0.15);
        }

        button:active {
            transform: translateY(0);
        }

        /* Transcript text styling */
        .transcript-container p {
            margin: 0.4rem 0;
            padding: 0.6rem;
            background: var(--background-cream);
            border-radius: 4px;
            line-height: 1.4;
            font-size: 0.95rem;
        }

        /* Custom scrollbar - made thinner */
        .transcript-container::-webkit-scrollbar {
            width: 6px;
        }

        .transcript-container::-webkit-scrollbar-track {
            background: var(--background-cream);
            border-radius: 3px;
        }

        .transcript-container::-webkit-scrollbar-thumb {
            background: #e66465;
            border-radius: 3px;
            opacity: 0.8;
        }

        .transcript-container::-webkit-scrollbar-thumb:hover {
            background: #f9a45c;
        }

        /* Add styles for toast notifications */
        .toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 16px 24px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            display: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .toast.error {
            background-color: #f44336;
            color: white;
        }

        .toast.warning {
            background-color: #ffd700;
            color: black;
        }

        /* Add styles for audio visualization */
        .icon-with-spinner {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            min-width: 180px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid white;
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            flex-shrink: 0;
        }

        .pulse-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            min-width: 180px;
        }

        .pulse-circle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: white;
            opacity: 0.2;
            flex-shrink: 0;
            transform: translateX(-0%) scale(var(--audio-level, 1));
            transition: transform 0.1s ease;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <!-- Add toast element after body opening tag -->
    <div id="error-toast" class="toast"></div>
    <div class="hero">
        <h1>Real-time Transcription</h1>
        <p>Powered by Groq and FastRTC</p>
    </div>

    <div class="container">
        <div class="transcript-container" id="transcript">
        </div>
        <div class="controls">
            <button id="start-button">Start Recording</button>
        </div>
    </div>

    <script>
        let peerConnection;
        let webrtc_id;
        let audioContext, analyser, audioSource;
        let audioLevel = 0;
        let animationFrame;

        const startButton = document.getElementById('start-button');
        const transcriptDiv = document.getElementById('transcript');

        function showError(message) {
            const toast = document.getElementById('error-toast');
            toast.textContent = message;
            toast.style.display = 'block';

            // Hide toast after 5 seconds
            setTimeout(() => {
                toast.style.display = 'none';
            }, 5000);
        }

        async function handleMessage(event) {
            // Handle any WebRTC data channel messages if needed
            const eventJson = JSON.parse(event.data);
            if (eventJson.type === "error") {
                showError(eventJson.message);
            } else if (eventJson.type === "send_input") {
                const response = await fetch('/send_input', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        webrtc_id: webrtc_id,
                        transcript: ""
                    })
                });
            }
            console.log('Received message:', event.data);

        }

        function updateButtonState() {
            if (peerConnection && (peerConnection.connectionState === 'connecting' || peerConnection.connectionState === 'new')) {
                startButton.innerHTML = `
                    <div class="icon-with-spinner">
                        <div class="spinner"></div>
                        <span>Connecting...</span>
                    </div>
                `;
            } else if (peerConnection && peerConnection.connectionState === 'connected') {
                startButton.innerHTML = `
                    <div class="pulse-container">
                        <div class="pulse-circle"></div>
                        <span>Stop Recording</span>
                    </div>
                `;
            } else {
                startButton.innerHTML = 'Start Recording';
            }
        }

        function setupAudioVisualization(stream) {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            analyser = audioContext.createAnalyser();
            audioSource = audioContext.createMediaStreamSource(stream);
            audioSource.connect(analyser);
            analyser.fftSize = 64;
            const dataArray = new Uint8Array(analyser.frequencyBinCount);

            function updateAudioLevel() {
                analyser.getByteFrequencyData(dataArray);
                const average = Array.from(dataArray).reduce((a, b) => a + b, 0) / dataArray.length;
                audioLevel = average / 255;

                const pulseCircle = document.querySelector('.pulse-circle');
                if (pulseCircle) {
                    pulseCircle.style.setProperty('--audio-level', 1 + audioLevel);
                }

                animationFrame = requestAnimationFrame(updateAudioLevel);
            }
            updateAudioLevel();
        }

        async function setupWebRTC() {
            const config = __RTC_CONFIGURATION__;
            peerConnection = new RTCPeerConnection(config);

            const timeoutId = setTimeout(() => {
                const toast = document.getElementById('error-toast');
                toast.textContent = "Connection is taking longer than usual. Are you on a VPN?";
                toast.className = 'toast warning';
                toast.style.display = 'block';

                // Hide warning after 5 seconds
                setTimeout(() => {
                    toast.style.display = 'none';
                }, 5000);
            }, 5000);

            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: true
                });

                setupAudioVisualization(stream);

                stream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, stream);
                });

                // Add connection state change listener
                peerConnection.addEventListener('connectionstatechange', () => {
                    console.log('connectionstatechange', peerConnection.connectionState);
                    if (peerConnection.connectionState === 'connected') {
                        clearTimeout(timeoutId);
                        const toast = document.getElementById('error-toast');
                        toast.style.display = 'none';
                    }
                    updateButtonState();
                });

                // Create data channel for messages
                const dataChannel = peerConnection.createDataChannel('text');
                dataChannel.onmessage = handleMessage;

                // Create and send offer
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);

                await new Promise((resolve) => {
                    if (peerConnection.iceGatheringState === "complete") {
                        resolve();
                    } else {
                        const checkState = () => {
                            if (peerConnection.iceGatheringState === "complete") {
                                peerConnection.removeEventListener("icegatheringstatechange", checkState);
                                resolve();
                            }
                        };
                        peerConnection.addEventListener("icegatheringstatechange", checkState);
                    }
                });

                webrtc_id = Math.random().toString(36).substring(7);

                const response = await fetch('/webrtc/offer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sdp: peerConnection.localDescription.sdp,
                        type: peerConnection.localDescription.type,
                        webrtc_id: webrtc_id
                    })
                });

                const serverResponse = await response.json();

                if (serverResponse.status === 'failed') {
                    showError(serverResponse.meta.error === 'concurrency_limit_reached'
                        ? `Too many connections. Maximum limit is ${serverResponse.meta.limit}`
                        : serverResponse.meta.error);
                    stop();
                    startButton.textContent = 'Start Recording';
                    return;
                }

                await peerConnection.setRemoteDescription(serverResponse);

                // Create event stream to receive transcripts
                const eventSource = new EventSource('/transcript?webrtc_id=' + webrtc_id);
                eventSource.addEventListener("output", (event) => {
                    appendTranscript(event.data);
                });
            } catch (err) {
                clearTimeout(timeoutId);
                console.error('Error setting up WebRTC:', err);
                showError('Failed to establish connection. Please try again.');
                stop();
                startButton.textContent = 'Start Recording';
            }
        }

        function appendTranscript(text) {
            const p = document.createElement('p');
            p.textContent = text;
            transcriptDiv.appendChild(p);
            transcriptDiv.scrollTop = transcriptDiv.scrollHeight;
        }

        function stop() {
            if (animationFrame) {
                cancelAnimationFrame(animationFrame);
            }
            if (audioContext) {
                audioContext.close();
                audioContext = null;
                analyser = null;
                audioSource = null;
            }
            if (peerConnection) {
                if (peerConnection.getTransceivers) {
                    peerConnection.getTransceivers().forEach(transceiver => {
                        if (transceiver.stop) {
                            transceiver.stop();
                        }
                    });
                }

                if (peerConnection.getSenders) {
                    peerConnection.getSenders().forEach(sender => {
                        if (sender.track && sender.track.stop) sender.track.stop();
                    });
                }

                setTimeout(() => {
                    peerConnection.close();
                }, 500);
            }
            audioLevel = 0;
            updateButtonState();
        }

        startButton.addEventListener('click', () => {
            if (startButton.textContent === 'Start Recording') {
                setupWebRTC();
            } else {
                stop();
            }
        });
    </script>
</body>

</html>