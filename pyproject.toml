[build-system]
requires = [
  "hatchling",
  "hatch-requirements-txt",
  "hatch-fancy-pypi-readme>=22.5.0",
]
build-backend = "hatchling.build"

[project]
name = "fastrtc"
version = "0.0.19.dev"
description = "The realtime communication library for Python"
readme = "README.md"
license = "apache-2.0"
requires-python = ">=3.10"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
keywords = [
  "streaming",
  "webrtc",
  "realtime",
  "machine learning",
  "computer vision",
  "audio",
  "video",
  "image",
  "audio processing",
  "video processing",
  "gradio-custom-component",
]
# Add dependencies here
dependencies = [
  "gradio>=4.0,<6.0",
  "aiortc",
  "audioop-lts;python_version>='3.13'",
  "librosa",
  "numpy<=1.26.4",       
  "numba>=0.60.0",
  "standard-aifc;python_version>='3.13'",
  "standard-sunau;python_version>='3.13'",
]
classifiers = [
  'Development Status :: 3 - Alpha',
  'Operating System :: OS Independent',
  'Programming Language :: Python :: 3',
  'Programming Language :: Python :: 3 :: Only',
  'Programming Language :: Python :: 3.10',
  'Programming Language :: Python :: 3.11',
  'Programming Language :: Python :: 3.12',
  'Programming Language :: Python :: 3.13',
  'Topic :: Internet',
  "Topic :: Software Development :: Libraries :: Application Frameworks",
  "Topic :: Software Development :: Libraries :: Python Modules",
  "Topic :: Software Development :: Libraries",
  "Topic :: Software Development",
  'Topic :: Scientific/Engineering',
  'Topic :: Scientific/Engineering :: Artificial Intelligence',
  'Topic :: Scientific/Engineering :: Visualization',
]

# The repository and space URLs are optional, but recommended.
# Adding a repository URL will create a badge in the auto-generated README that links to the repository.
# Adding a space URL will create a badge in the auto-generated README that links to the space.
# This will make it easy for people to find your deployed demo or source code when they
# encounter your project in the wild.

[project.urls]
repository = "https://github.com/freddyaboulton/gradio-webrtc"
issues = "https://github.com/freddyaboulton/gradio-webrtc/issues"
Documentation = "https://freddyaboulton.github.io/gradio-webrtc/cookbook/"

[project.optional-dependencies]
dev = ["build", "twine"]
vad = ["onnxruntime>=1.20.1"]
tts = ["kokoro-onnx"]
stopword = ["fastrtc-moonshine-onnx", "onnxruntime>=1.20.1"]
stt = ["fastrtc-moonshine-onnx", "onnxruntime>=1.20.1"]

[tool.hatch.build]
artifacts = ["/backend/fastrtc/templates", "*.pyi"]

[tool.hatch.build.targets.wheel]
packages = ["/backend/fastrtc"]

[tool.ruff]
target-version = "py310"
extend-exclude = ["demo/phonic_chat", "demo/nextjs_voice_chat"]
