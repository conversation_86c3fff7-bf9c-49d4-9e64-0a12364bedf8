{#-
This file was automatically generated - do not edit
-#}
{% set class = "md-header" %}
{% if "navigation.tabs.sticky" in features %}
{% set class = class ~ " md-header--shadow md-header--lifted" %}
{% elif "navigation.tabs" not in features %}
{% set class = class ~ " md-header--shadow" %}
{% endif %}
<header class="{{ class }}" data-md-component="header">
    <nav class="md-header__inner md-grid" aria-label="{{ lang.t('header') }}">
        <a href="{{ config.extra.homepage | d(nav.homepage.url, true) | url }}" title="{{ config.site_name | e }}"
            class="md-header__button md-logo" aria-label="{{ config.site_name }}" data-md-component="logo">
            {% include "partials/logo.html" %}
        </a>
        <label class="md-header__button md-icon" for="__drawer">
            {% set icon = config.theme.icon.menu or "material/menu" %}
            {% include ".icons/" ~ icon ~ ".svg" %}
        </label>
        <div class="md-header__title" data-md-component="header-title">
            <div class="md-header__ellipsis">
                <div class="md-header__topic">
                    <span class="md-ellipsis">
                        {{ config.site_name }}
                    </span>
                </div>
                <div class="md-header__topic" data-md-component="header-topic">
                    <span class="md-ellipsis">
                        {% if page.meta and page.meta.title %}
                        {{ page.meta.title }}
                        {% else %}
                        {{ page.title }}
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
        {% if config.theme.palette %}
        {% if not config.theme.palette is mapping %}
        {% include "partials/palette.html" %}
        {% endif %}
        {% endif %}
        {% if not config.theme.palette is mapping %}
        {% include "partials/javascripts/palette.html" %}
        {% endif %}
        {% if config.extra.alternate %}
        {% include "partials/alternate.html" %}
        {% endif %}

        <div style="display: flex; align-items: center; margin-right: 1rem;">
            <a href="https://hf.co/fastrtc" target="_blank" rel="noopener noreferrer">
                <img src="/hf-logo.svg"
                    onerror="this.onerror=null; this.src='https://huggingface.co/datasets/freddyaboulton/bucket/resolve/main/hf-logo.svg';"
                    style="height: 24px; margin-right: 10px;">
            </a>
            <a href="https://gradio.app" target="_blank" rel="noopener noreferrer">
                <img src="/gradio-logo.svg"
                    onerror="this.onerror=null; this.src='https://huggingface.co/datasets/freddyaboulton/bucket/resolve/main/gradio-logo.svg';"
                    style="height: 24px; margin-right: 10px;">
            </a>
            <a href="https://discord.gg/TSWU7HyaYu" target="_blank" rel="noopener noreferrer">
                <img src="/Discord-Symbol-White.svg" style="height: 16px; margin-right: 10px;">
            </a>
        </div>

        {% if "material/search" in config.plugins %}
        <label class="md-header__button md-icon" for="__search">
            {% set icon = config.theme.icon.search or "material/magnify" %}
            {% include ".icons/" ~ icon ~ ".svg" %}
        </label>
        {% include "partials/search.html" %}
        {% endif %}
        {% if config.repo_url %}
        <div class="md-header__source">
            {% include "partials/source.html" %}
        </div>
        {% endif %}
    </nav>
    {% if "navigation.tabs.sticky" in features %}
    {% if "navigation.tabs" in features %}
    {% include "partials/tabs.html" %}
    {% endif %}
    {% endif %}
</header>