---
title: Whisper Realtime Transcription
emoji: 👂
colorFrom: purple
colorTo: red
sdk: gradio
sdk_version: 5.16.0
app_file: app.py
pinned: false
license: mit
short_description: Transcribe audio in realtime with Whisper
tags: [webrtc, websocket, gradio, secret|TWILIO_ACCOUNT_SID, secret|TWILIO_AUTH_TOKEN, secret|GROQ_API_KEY]
---

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference