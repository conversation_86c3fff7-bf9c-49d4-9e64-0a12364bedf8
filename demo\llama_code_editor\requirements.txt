# This file was autogenerated by uv via the following command:
#    uv pip compile demo/llama_code_editor/requirements.in -o demo/llama_code_editor/requirements.txt
aiofiles==23.2.1
    # via gradio
aiohappyeyeballs==2.4.6
    # via aiohttp
aiohttp==3.11.12
    # via
    #   aiohttp-retry
    #   twilio
aiohttp-retry==2.9.1
    # via twilio
aioice==0.9.0
    # via aiortc
aiortc==1.10.1
    # via fastrtc
aiosignal==1.3.2
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==4.6.2.post1
    # via
    #   gradio
    #   groq
    #   httpx
    #   openai
    #   starlette
attrs==25.1.0
    # via aiohttp
audioread==3.0.1
    # via librosa
av==12.3.0
    # via aiortc
certifi==2024.8.30
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via
    #   aiortc
    #   cryptography
    #   pylibsrtp
    #   soundfile
charset-normalizer==3.4.0
    # via requests
click==8.1.7
    # via
    #   typer
    #   uvicorn
coloredlogs==15.0.1
    # via onnxruntime
cryptography==43.0.3
    # via
    #   aiortc
    #   pyopenssl
decorator==5.1.1
    # via librosa
distro==1.9.0
    # via
    #   groq
    #   openai
dnspython==2.7.0
    # via aioice
fastapi==0.115.5
    # via gradio
fastrtc==0.0.2.post4
    # via -r demo/llama_code_editor/requirements.in
ffmpy==0.4.0
    # via gradio
filelock==3.16.1
    # via huggingface-hub
flatbuffers==24.3.25
    # via onnxruntime
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2024.10.0
    # via
    #   gradio-client
    #   huggingface-hub
google-crc32c==1.6.0
    # via aiortc
gradio==5.16.0
    # via fastrtc
gradio-client==1.7.0
    # via gradio
groq==0.18.0
    # via -r demo/llama_code_editor/requirements.in
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.7
    # via httpx
httpx==0.27.2
    # via
    #   gradio
    #   gradio-client
    #   groq
    #   openai
    #   safehttpx
huggingface-hub==0.28.1
    # via
    #   gradio
    #   gradio-client
humanfriendly==10.0
    # via coloredlogs
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
ifaddr==0.2.0
    # via aioice
jinja2==3.1.4
    # via gradio
jiter==0.7.1
    # via openai
joblib==1.4.2
    # via
    #   librosa
    #   scikit-learn
lazy-loader==0.4
    # via librosa
librosa==0.10.2.post1
    # via fastrtc
llvmlite==0.43.0
    # via numba
markdown-it-py==3.0.0
    # via rich
markupsafe==2.1.5
    # via
    #   gradio
    #   jinja2
mdurl==0.1.2
    # via markdown-it-py
mpmath==1.3.0
    # via sympy
msgpack==1.1.0
    # via librosa
multidict==6.1.0
    # via
    #   aiohttp
    #   yarl
numba==0.60.0
    # via librosa
numpy==2.0.2
    # via
    #   gradio
    #   librosa
    #   numba
    #   onnxruntime
    #   pandas
    #   scikit-learn
    #   scipy
    #   soxr
onnxruntime==1.20.1
    # via fastrtc
openai==1.54.4
    # via -r demo/llama_code_editor/requirements.in
orjson==3.10.11
    # via gradio
packaging==24.2
    # via
    #   gradio
    #   gradio-client
    #   huggingface-hub
    #   lazy-loader
    #   onnxruntime
    #   pooch
pandas==2.2.3
    # via gradio
pillow==11.0.0
    # via gradio
platformdirs==4.3.6
    # via pooch
pooch==1.8.2
    # via librosa
propcache==0.2.1
    # via
    #   aiohttp
    #   yarl
protobuf==5.28.3
    # via onnxruntime
pycparser==2.22
    # via cffi
pydantic==2.9.2
    # via
    #   fastapi
    #   gradio
    #   groq
    #   openai
pydantic-core==2.23.4
    # via pydantic
pydub==0.25.1
    # via gradio
pyee==12.1.1
    # via aiortc
pygments==2.18.0
    # via rich
pyjwt==2.10.1
    # via twilio
pylibsrtp==0.10.0
    # via aiortc
pyopenssl==24.2.1
    # via aiortc
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.0.1
    # via -r demo/llama_code_editor/requirements.in
python-multipart==0.0.20
    # via gradio
pytz==2024.2
    # via pandas
pyyaml==6.0.2
    # via
    #   gradio
    #   huggingface-hub
requests==2.32.3
    # via
    #   huggingface-hub
    #   pooch
    #   twilio
rich==13.9.4
    # via typer
ruff==0.9.6
    # via gradio
safehttpx==0.1.6
    # via gradio
scikit-learn==1.5.2
    # via librosa
scipy==1.14.1
    # via
    #   librosa
    #   scikit-learn
semantic-version==2.10.0
    # via gradio
shellingham==1.5.4
    # via typer
six==1.16.0
    # via python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   groq
    #   httpx
    #   openai
soundfile==0.12.1
    # via librosa
soxr==0.5.0.post1
    # via librosa
starlette==0.41.3
    # via
    #   fastapi
    #   gradio
sympy==1.13.3
    # via onnxruntime
threadpoolctl==3.5.0
    # via scikit-learn
tomlkit==0.12.0
    # via gradio
tqdm==4.67.0
    # via
    #   huggingface-hub
    #   openai
twilio==9.4.5
    # via -r demo/llama_code_editor/requirements.in
typer==0.13.1
    # via gradio
typing-extensions==4.12.2
    # via
    #   fastapi
    #   gradio
    #   gradio-client
    #   groq
    #   huggingface-hub
    #   librosa
    #   openai
    #   pydantic
    #   pydantic-core
    #   pyee
    #   typer
tzdata==2024.2
    # via pandas
urllib3==2.2.3
    # via requests
uvicorn==0.32.0
    # via gradio
websockets==12.0
    # via gradio-client
yarl==1.18.3
    # via aiohttp
