---
app_file: app.py
colorFrom: purple
colorTo: red
emoji: 👂
license: mit
pinned: false
sdk: gradio
sdk_version: 5.16.0
short_description: Transcribe audio in realtime - Gradio UI version
tags:
- webrtc
- websocket
- gradio
- secret|TWILIO_ACCOUNT_SID
- secret|TWILIO_AUTH_TOKEN
- secret|GROQ_API_KEY
title: Whisper Realtime Transcription (Gradio UI)
---


Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference