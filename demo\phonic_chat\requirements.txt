# This file was autogenerated by uv via the following command:
#    uv pip compile requirements.in -o requirements.txt
aiohappyeyeballs==2.4.6
    # via aiohttp
aiohttp==3.11.12
    # via
    #   aiohttp-retry
    #   twilio
aiohttp-retry==2.9.1
    # via twilio
aiosignal==1.3.2
    # via aiohttp
attrs==25.1.0
    # via aiohttp
certifi==2025.1.31
    # via requests
cffi==1.17.1
    # via sounddevice
charset-normalizer==3.4.1
    # via requests
fastrtc==0.0.1
    # via -r requirements.in
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
idna==3.10
    # via
    #   requests
    #   yarl
isort==6.0.0
    # via phonic-python
loguru==0.7.3
    # via phonic-python
multidict==6.1.0
    # via
    #   aiohttp
    #   yarl
numpy==2.2.3
    # via
    #   phonic-python
    #   scipy
phonic-python==0.1.3
    # via -r requirements.in
propcache==0.3.0
    # via
    #   aiohttp
    #   yarl
pycparser==2.22
    # via cffi
pyjwt==2.10.1
    # via twilio
python-dotenv==1.0.1
    # via
    #   -r requirements.in
    #   phonic-python
requests==2.32.3
    # via
    #   phonic-python
    #   twilio
scipy==1.15.2
    # via phonic-python
sounddevice==0.5.1
    # via phonic-python
twilio==9.4.6
    # via -r requirements.in
typing-extensions==4.12.2
    # via phonic-python
urllib3==2.3.0
    # via requests
websockets==15.0
    # via phonic-python
yarl==1.18.3
    # via aiohttp
